"""
Task logging utilities for Celery tasks
"""

import json
import time
from datetime import datetime
from typing import Any, Dict, Optional, Union
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)


class TaskLogger:
    """Utility class for logging task execution details"""
    
    def __init__(self):
        self.logger = structlog.get_logger("celery.tasks")
    
    def log_task_start(self, task_id: str, task_name: str, args: tuple, kwargs: dict) -> None:
        """Log task start"""
        self.logger.info(
            "Task started",
            task_id=task_id,
            task_name=task_name,
            args=self._sanitize_args(args),
            kwargs=self._sanitize_kwargs(kwargs),
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_task_success(self, task_id: str, task_name: str, result: Any, 
                        args: tuple, kwargs: dict) -> None:
        """Log task success"""
        self.logger.info(
            "Task completed successfully",
            task_id=task_id,
            task_name=task_name,
            result_type=type(result).__name__,
            result_size=len(str(result)) if result else 0,
            args=self._sanitize_args(args),
            kwargs=self._sanitize_kwargs(kwargs),
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_task_failure(self, task_id: str, task_name: str, error: str, 
                        traceback: str, args: tuple, kwargs: dict) -> None:
        """Log task failure"""
        self.logger.error(
            "Task failed",
            task_id=task_id,
            task_name=task_name,
            error=error,
            traceback=traceback if settings.DEBUG else None,
            args=self._sanitize_args(args),
            kwargs=self._sanitize_kwargs(kwargs),
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_task_retry(self, task_id: str, task_name: str, error: str, 
                      retry_count: int, args: tuple, kwargs: dict) -> None:
        """Log task retry"""
        self.logger.warning(
            "Task retrying",
            task_id=task_id,
            task_name=task_name,
            error=error,
            retry_count=retry_count,
            args=self._sanitize_args(args),
            kwargs=self._sanitize_kwargs(kwargs),
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_task_timing(self, task_id: str, task_name: str, execution_time: float) -> None:
        """Log task execution timing"""
        self.logger.info(
            "Task execution timing",
            task_id=task_id,
            task_name=task_name,
            execution_time=execution_time,
            execution_time_ms=execution_time * 1000,
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_task_progress(self, task_id: str, task_name: str, progress: int, 
                         total: int, message: str = None) -> None:
        """Log task progress"""
        self.logger.info(
            "Task progress",
            task_id=task_id,
            task_name=task_name,
            progress=progress,
            total=total,
            percentage=round((progress / total) * 100, 2) if total > 0 else 0,
            message=message,
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_task_custom(self, task_id: str, task_name: str, event: str, 
                       data: Dict[str, Any] = None) -> None:
        """Log custom task event"""
        log_data = {
            "task_id": task_id,
            "task_name": task_name,
            "event": event,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        if data:
            log_data.update(self._sanitize_kwargs(data))
        
        self.logger.info("Task custom event", **log_data)
    
    def _sanitize_args(self, args: tuple) -> list:
        """Sanitize task arguments for logging"""
        if not args:
            return []
        
        sanitized = []
        for arg in args:
            sanitized.append(self._sanitize_value(arg))
        
        return sanitized
    
    def _sanitize_kwargs(self, kwargs: dict) -> dict:
        """Sanitize task keyword arguments for logging"""
        if not kwargs:
            return {}
        
        sanitized = {}
        sensitive_keys = {
            'password', 'token', 'api_key', 'secret', 'auth', 'authorization',
            'green_api_token', 'deepseek_api_key', 'smtp_password'
        }
        
        for key, value in kwargs.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = "***REDACTED***"
            else:
                sanitized[key] = self._sanitize_value(value)
        
        return sanitized
    
    def _sanitize_value(self, value: Any) -> Any:
        """Sanitize individual values for logging"""
        if isinstance(value, (str, int, float, bool, type(None))):
            return value
        elif isinstance(value, dict):
            return self._sanitize_kwargs(value)
        elif isinstance(value, (list, tuple)):
            return [self._sanitize_value(item) for item in value]
        else:
            # For complex objects, just return their type
            return f"<{type(value).__name__}>"


class TaskMetrics:
    """Utility class for collecting task metrics"""
    
    def __init__(self):
        self.logger = structlog.get_logger("celery.metrics")
    
    def record_task_duration(self, task_name: str, duration: float) -> None:
        """Record task execution duration"""
        self.logger.info(
            "Task duration metric",
            task_name=task_name,
            duration=duration,
            metric_type="duration",
            timestamp=datetime.utcnow().isoformat()
        )
    
    def record_task_count(self, task_name: str, status: str) -> None:
        """Record task count by status"""
        self.logger.info(
            "Task count metric",
            task_name=task_name,
            status=status,
            metric_type="count",
            timestamp=datetime.utcnow().isoformat()
        )
    
    def record_queue_size(self, queue_name: str, size: int) -> None:
        """Record queue size"""
        self.logger.info(
            "Queue size metric",
            queue_name=queue_name,
            size=size,
            metric_type="queue_size",
            timestamp=datetime.utcnow().isoformat()
        )
    
    def record_worker_count(self, worker_count: int) -> None:
        """Record active worker count"""
        self.logger.info(
            "Worker count metric",
            worker_count=worker_count,
            metric_type="worker_count",
            timestamp=datetime.utcnow().isoformat()
        )


class TaskAuditLogger:
    """Audit logger for sensitive task operations"""
    
    def __init__(self):
        self.logger = structlog.get_logger("celery.audit")
    
    def log_sensitive_operation(self, task_id: str, task_name: str, 
                               operation: str, hotel_id: Optional[int] = None,
                               user_id: Optional[int] = None, 
                               details: Optional[Dict[str, Any]] = None) -> None:
        """Log sensitive task operations for audit purposes"""
        audit_data = {
            "task_id": task_id,
            "task_name": task_name,
            "operation": operation,
            "timestamp": datetime.utcnow().isoformat(),
            "audit_type": "task_operation"
        }
        
        if hotel_id:
            audit_data["hotel_id"] = hotel_id
        
        if user_id:
            audit_data["user_id"] = user_id
        
        if details:
            audit_data["details"] = self._sanitize_audit_details(details)
        
        self.logger.info("Task audit log", **audit_data)
    
    def _sanitize_audit_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize audit details"""
        sanitized = {}
        sensitive_keys = {'password', 'token', 'api_key', 'secret'}
        
        for key, value in details.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = "***REDACTED***"
            else:
                sanitized[key] = str(value)[:100]  # Limit length
        
        return sanitized


# Global instances
task_logger = TaskLogger()
task_metrics = TaskMetrics()
task_audit_logger = TaskAuditLogger()

# Export all components
__all__ = [
    'TaskLogger',
    'TaskMetrics', 
    'TaskAuditLogger',
    'task_logger',
    'task_metrics',
    'task_audit_logger'
]
